#!/usr/bin/env python3.10
"""
Simple test script to verify GCU integration
"""

import sys
import torch
from nanovllm.utils.gcu_utils import (
    is_gcu_available, get_device_name, get_device_count, 
    get_current_device, get_memory_info, get_memory_usage
)

def test_gcu_basic():
    """Test basic GCU functionality"""
    print("=== GCU Basic Test ===")
    print(f"GCU Available: {is_gcu_available()}")
    print(f"Device Name: {get_device_name()}")
    print(f"Device Count: {get_device_count()}")
    print(f"Current Device: {get_current_device()}")
    
    try:
        memory_info = get_memory_info()
        print(f"Memory Info: Free={memory_info[0]//1024//1024}MB, Total={memory_info[1]//1024//1024}MB")
    except Exception as e:
        print(f"Memory Info Error: {e}")
    
    try:
        memory_usage = get_memory_usage()
        print(f"Memory Usage: {memory_usage}")
    except Exception as e:
        print(f"Memory Usage Error: {e}")

def test_tensor_operations():
    """Test basic tensor operations on GCU"""
    print("\n=== Tensor Operations Test ===")
    device_name = get_device_name()
    
    try:
        # Create tensors
        x = torch.randn(4, 4).to(device_name)
        y = torch.randn(4, 4).to(device_name)
        
        print(f"Created tensors on {x.device}")
        
        # Basic operations
        z = x + y
        print(f"Addition result shape: {z.shape}")
        
        # Matrix multiplication
        w = torch.mm(x, y)
        print(f"Matrix multiplication result shape: {w.shape}")
        
        print("✓ Basic tensor operations successful")
        
    except Exception as e:
        print(f"✗ Tensor operations failed: {e}")
        import traceback
        traceback.print_exc()

def test_attention_components():
    """Test attention-related components"""
    print("\n=== Attention Components Test ===")
    
    try:
        from nanovllm.layers.attention import Attention
        from nanovllm.utils.context import set_context, reset_context
        
        # Create a simple attention layer
        attention = Attention(
            num_heads=8,
            head_dim=64,
            scale=0.125,
            num_kv_heads=8
        )
        
        device_name = get_device_name()
        attention = attention.to(device_name)
        
        # Create dummy inputs
        batch_size, seq_len = 2, 10
        hidden_size = 8 * 64  # num_heads * head_dim
        
        q = torch.randn(batch_size * seq_len, hidden_size).to(device_name)
        k = torch.randn(batch_size * seq_len, hidden_size).to(device_name)
        v = torch.randn(batch_size * seq_len, hidden_size).to(device_name)
        
        print(f"Created attention inputs on {q.device}")
        
        # Set up context (simplified)
        cu_seqlens_q = torch.tensor([0, seq_len, 2*seq_len], dtype=torch.int32).to(device_name)
        cu_seqlens_k = torch.tensor([0, seq_len, 2*seq_len], dtype=torch.int32).to(device_name)
        slot_mapping = torch.arange(batch_size * seq_len, dtype=torch.int32).to(device_name)
        
        set_context(
            is_prefill=True,
            cu_seqlens_q=cu_seqlens_q,
            cu_seqlens_k=cu_seqlens_k,
            max_seqlen_q=seq_len,
            max_seqlen_k=seq_len,
            slot_mapping=slot_mapping
        )
        
        # Test attention forward pass
        try:
            output = attention(q, k, v)
            print(f"Attention output shape: {output.shape}")
            print("✓ Attention forward pass successful")
        except Exception as e:
            print(f"✗ Attention forward pass failed: {e}")
            # Try without flash attention (fallback)
            print("Trying fallback implementation...")
            import traceback
            traceback.print_exc()
        
        reset_context()
        
    except Exception as e:
        print(f"✗ Attention components test failed: {e}")
        import traceback
        traceback.print_exc()

def test_linear_layers():
    """Test linear layer components"""
    print("\n=== Linear Layers Test ===")
    
    try:
        from nanovllm.layers.linear import ReplicatedLinear, ColumnParallelLinear, RowParallelLinear
        
        device_name = get_device_name()
        
        # Test ReplicatedLinear
        linear = ReplicatedLinear(512, 256)
        linear = linear.to(device_name)
        
        x = torch.randn(4, 512).to(device_name)
        y = linear(x)
        print(f"ReplicatedLinear output shape: {y.shape}")
        
        print("✓ Linear layers test successful")
        
    except Exception as e:
        print(f"✗ Linear layers test failed: {e}")
        import traceback
        traceback.print_exc()

def test_activation_functions():
    """Test activation functions"""
    print("\n=== Activation Functions Test ===")
    
    try:
        from nanovllm.layers.activation import SiluAndMul
        
        device_name = get_device_name()
        
        # Test SiluAndMul
        activation = SiluAndMul()
        activation = activation.to(device_name)
        
        x = torch.randn(4, 1024).to(device_name)  # Double size for SiluAndMul
        y = activation(x)
        print(f"SiluAndMul output shape: {y.shape}")
        
        print("✓ Activation functions test successful")
        
    except Exception as e:
        print(f"✗ Activation functions test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run all tests"""
    print("Starting nano-vllm GCU integration tests...")
    
    test_gcu_basic()
    test_tensor_operations()
    test_linear_layers()
    test_activation_functions()
    test_attention_components()
    
    print("\n=== Test Summary ===")
    print("Tests completed. Check output above for any failures.")

if __name__ == "__main__":
    main()
