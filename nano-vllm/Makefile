# Makefile for nano-vllm with GCU support
# Uses Python 3.10 for development and debugging

# Python configuration
PYTHON := python3.10
PIP := $(PYTHON) -m pip

# Project directories
PROJECT_DIR := $(shell pwd)
NANOVLLM_DIR := $(PROJECT_DIR)/nanovllm

# GCU environment variables
export TOPS_VISIBLE_DEVICES ?= 0
export PYTHONPATH := $(PROJECT_DIR):$(PYTHONPATH)

# Default target
.PHONY: all
all: install

# Installation targets
.PHONY: install install-dev install-gcu
install:
	@echo "Installing nano-vllm..."
	$(PIP) install -e .

install-dev: install
	@echo "Installing development dependencies..."
	$(PIP) install pytest black isort flake8 mypy

install-gcu:
	@echo "Installing GCU dependencies..."
	@echo "Note: Make sure torch_gcu and tops_extension are installed in your environment"
	@echo "These should be installed from the GCU SDK"

# Development targets
.PHONY: format lint type-check test
format:
	@echo "Formatting code..."
	black $(NANOVLLM_DIR)
	isort $(NANOVLLM_DIR)

lint:
	@echo "Linting code..."
	flake8 $(NANOVLLM_DIR) --max-line-length=120 --ignore=E203,W503

type-check:
	@echo "Type checking..."
	mypy $(NANOVLLM_DIR) --ignore-missing-imports

test:
	@echo "Running tests..."
	$(PYTHON) -m pytest tests/ -v

# Debug and run targets
.PHONY: debug run-example check-gcu
debug: check-gcu
	@echo "Running example.py in debug mode..."
	$(PYTHON) -u example.py

run-example: check-gcu
	@echo "Running example.py..."
	$(PYTHON) example.py

check-gcu:
	@echo "Checking GCU availability..."
	@$(PYTHON) -c "import torch; print(f'PyTorch version: {torch.__version__}')" || (echo "PyTorch not found" && exit 1)
	@$(PYTHON) -c "import torch_gcu; print('GCU support: Available')" || echo "GCU support: Not available (will use CPU/CUDA fallback)"
	@$(PYTHON) -c "import tops_extension.torch; print('TOPS extension: Available')" || echo "TOPS extension: Not available"
	@$(PYTHON) -c "from nanovllm.utils.gcu_utils import is_gcu_available; print(f'nano-vllm GCU support: {is_gcu_available()}')"

# Environment setup
.PHONY: setup-env
setup-env:
	@echo "Setting up environment..."
	@echo "export TOPS_VISIBLE_DEVICES=0" >> ~/.bashrc
	@echo "export PYTHONPATH=$(PROJECT_DIR):\$$PYTHONPATH" >> ~/.bashrc
	@echo "Environment variables added to ~/.bashrc"
	@echo "Please run 'source ~/.bashrc' or restart your shell"

# Model and data management
.PHONY: download-model
download-model:
	@echo "Model download should be handled separately"
	@echo "Make sure your model is available at the path specified in example.py"
	@echo "Default path: /opt/xx/models/Qwen3-0.6B"

# Benchmarking
.PHONY: benchmark
benchmark: check-gcu
	@echo "Running benchmark..."
	$(PYTHON) bench.py

# Memory profiling
.PHONY: profile-memory
profile-memory: check-gcu
	@echo "Running memory profiling..."
	$(PYTHON) -c "from nanovllm.utils.gcu_utils import get_memory_usage; print('Memory usage:', get_memory_usage())"
	$(PYTHON) example.py
	$(PYTHON) -c "from nanovllm.utils.gcu_utils import get_memory_usage; print('Memory usage after run:', get_memory_usage())"

# Clean targets
.PHONY: clean clean-cache clean-all
clean:
	@echo "Cleaning build artifacts..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/ dist/

clean-cache:
	@echo "Cleaning cache..."
	$(PYTHON) -c "from nanovllm.utils.gcu_utils import empty_cache; empty_cache()"

clean-all: clean clean-cache

# Docker support (if needed)
.PHONY: docker-build docker-run
docker-build:
	@echo "Building Docker image..."
	docker build -t nano-vllm-gcu .

docker-run:
	@echo "Running Docker container..."
	docker run --rm -it --gpus all nano-vllm-gcu

# Help target
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  install          - Install nano-vllm"
	@echo "  install-dev      - Install with development dependencies"
	@echo "  install-gcu      - Install GCU dependencies (manual step required)"
	@echo "  format           - Format code with black and isort"
	@echo "  lint             - Lint code with flake8"
	@echo "  type-check       - Type check with mypy"
	@echo "  test             - Run tests"
	@echo "  debug            - Run example.py in debug mode"
	@echo "  run-example      - Run example.py"
	@echo "  check-gcu        - Check GCU availability and configuration"
	@echo "  setup-env        - Setup environment variables"
	@echo "  download-model   - Instructions for model download"
	@echo "  benchmark        - Run benchmark"
	@echo "  profile-memory   - Profile memory usage"
	@echo "  clean            - Clean build artifacts"
	@echo "  clean-cache      - Clean device cache"
	@echo "  clean-all        - Clean everything"
	@echo "  docker-build     - Build Docker image"
	@echo "  docker-run       - Run Docker container"
	@echo "  help             - Show this help message"

# Development workflow
.PHONY: dev-setup dev-check
dev-setup: install-dev setup-env
	@echo "Development environment setup complete"

dev-check: format lint type-check test
	@echo "Development checks complete"

# Quick test with different configurations
.PHONY: test-gcu test-cuda test-cpu
test-gcu:
	@echo "Testing with GCU..."
	TOPS_VISIBLE_DEVICES=0 $(PYTHON) example.py

test-cuda:
	@echo "Testing with CUDA..."
	CUDA_VISIBLE_DEVICES=0 $(PYTHON) example.py

test-cpu:
	@echo "Testing with CPU..."
	CUDA_VISIBLE_DEVICES="" TOPS_VISIBLE_DEVICES="" $(PYTHON) example.py
