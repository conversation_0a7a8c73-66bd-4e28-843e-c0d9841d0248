"""
GCU utilities for nano-vllm
Provides GCU device management and compatibility layer
"""

import os
import torch
from typing import Optional, Union

# GCU imports
try:
    import torch_gcu
    import tops_extension.torch
    _GCU_AVAILABLE = True
except ImportError:
    _GCU_AVAILABLE = False
    torch_gcu = None

def is_gcu_available() -> bool:
    """Check if GCU is available"""
    return _GCU_AVAILABLE

def get_device_name() -> str:
    """Get the device name (gcu or cuda)"""
    if is_gcu_available():
        return "gcu"
    else:
        return "cuda"

def get_device_count() -> int:
    """Get the number of available devices"""
    if is_gcu_available():
        try:
            return torch_gcu.device_count()
        except AttributeError:
            # Fallback for different torch_gcu API
            try:
                return len(torch_gcu.get_device_properties())
            except:
                return 1  # Assume at least one device
    else:
        return torch.cuda.device_count()

def set_device(device_id: int):
    """Set the current device"""
    if is_gcu_available():
        try:
            torch_gcu.set_device(device_id)
        except AttributeError:
            # Some torch_gcu versions might not have set_device
            pass
    else:
        torch.cuda.set_device(device_id)

def get_current_device() -> int:
    """Get the current device ID"""
    if is_gcu_available():
        try:
            return torch_gcu.current_device()
        except AttributeError:
            # Fallback for different torch_gcu API
            return 0  # Assume device 0
    else:
        return torch.cuda.current_device()

def synchronize():
    """Synchronize the current device"""
    if is_gcu_available():
        try:
            torch_gcu.synchronize()
        except AttributeError:
            # Some torch_gcu versions might not have synchronize
            pass
    else:
        torch.cuda.synchronize()

def empty_cache():
    """Empty the device cache"""
    if is_gcu_available():
        try:
            torch_gcu.empty_cache()
        except AttributeError:
            # Some torch_gcu versions might not have empty_cache
            pass
    else:
        torch.cuda.empty_cache()

def get_device_properties(device_id: int = None):
    """Get device properties"""
    if device_id is None:
        device_id = get_current_device()
    
    if is_gcu_available():
        return torch_gcu.get_device_properties(device_id)
    else:
        return torch.cuda.get_device_properties(device_id)

def get_memory_info(device_id: int = None):
    """Get memory information"""
    if device_id is None:
        device_id = get_current_device()

    if is_gcu_available():
        try:
            return torch_gcu.mem_get_info(device_id)
        except AttributeError:
            # Fallback for different torch_gcu API
            try:
                return torch_gcu.memory_stats()
            except:
                # Return dummy values if not available
                return (8 * 1024**3, 16 * 1024**3)  # 8GB free, 16GB total
    else:
        return torch.cuda.mem_get_info(device_id)

def to_device(tensor: torch.Tensor, device_id: int = None) -> torch.Tensor:
    """Move tensor to device"""
    if device_id is None:
        device_id = get_current_device()
    
    device_name = get_device_name()
    return tensor.to(f"{device_name}:{device_id}")

def get_stream():
    """Get current stream"""
    if is_gcu_available():
        return torch_gcu.current_stream()
    else:
        return torch.cuda.current_stream()

class DeviceContext:
    """Context manager for device operations"""
    
    def __init__(self, device_id: int):
        self.device_id = device_id
        self.prev_device = None
    
    def __enter__(self):
        self.prev_device = get_current_device()
        set_device(self.device_id)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.prev_device is not None:
            set_device(self.prev_device)

def init_distributed():
    """Initialize distributed training"""
    if is_gcu_available():
        # GCU distributed initialization
        if not torch.distributed.is_initialized():
            torch.distributed.init_process_group(backend='eccl')
    else:
        # CUDA distributed initialization
        if not torch.distributed.is_initialized():
            torch.distributed.init_process_group(backend='nccl')

def get_world_size() -> int:
    """Get world size for distributed training"""
    if torch.distributed.is_initialized():
        return torch.distributed.get_world_size()
    return 1

def get_rank() -> int:
    """Get rank for distributed training"""
    if torch.distributed.is_initialized():
        return torch.distributed.get_rank()
    return 0

def all_reduce(tensor: torch.Tensor, op=torch.distributed.ReduceOp.SUM):
    """All reduce operation"""
    if torch.distributed.is_initialized():
        torch.distributed.all_reduce(tensor, op=op)

def broadcast(tensor: torch.Tensor, src: int = 0):
    """Broadcast operation"""
    if torch.distributed.is_initialized():
        torch.distributed.broadcast(tensor, src=src)

# Environment setup
def setup_environment():
    """Setup environment variables for GCU"""
    if is_gcu_available():
        # Set GCU specific environment variables if needed
        os.environ.setdefault('TOPS_VISIBLE_DEVICES', '0')
        # Add other GCU specific environment setup here
    else:
        # Set CUDA specific environment variables
        os.environ.setdefault('CUDA_VISIBLE_DEVICES', '0')

# Memory management
def get_memory_usage():
    """Get current memory usage"""
    if is_gcu_available():
        allocated = torch_gcu.memory_allocated()
        cached = torch_gcu.memory_reserved()
    else:
        allocated = torch.cuda.memory_allocated()
        cached = torch.cuda.memory_reserved()
    
    return {
        'allocated': allocated,
        'cached': cached,
        'allocated_mb': allocated / 1024 / 1024,
        'cached_mb': cached / 1024 / 1024
    }

def reset_peak_memory_stats():
    """Reset peak memory statistics"""
    if is_gcu_available():
        torch_gcu.reset_peak_memory_stats()
    else:
        torch.cuda.reset_peak_memory_stats()

def get_max_memory_allocated():
    """Get maximum memory allocated"""
    if is_gcu_available():
        return torch_gcu.max_memory_allocated()
    else:
        return torch.cuda.max_memory_allocated()
