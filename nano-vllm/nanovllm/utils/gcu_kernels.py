"""
GCU kernels for nano-vllm
Provides GCU-based implementations of core operations based on vllm-vllm_enrigin
"""

import torch
from typing import Optional, List, Tuple
from .gcu_utils import is_gcu_available

# Import GCU operations if available
if is_gcu_available():
    try:
        import torch_gcu
        import tops_extension.torch
        # Try to import vllm_gcu operations
        try:
            import vllm_gcu._C as _gcu_ops
            _GCU_OPS_AVAILABLE = True
        except ImportError:
            _GCU_OPS_AVAILABLE = False
    except ImportError:
        _GCU_OPS_AVAILABLE = False
else:
    _GCU_OPS_AVAILABLE = False

# Partition size for paged attention v2
_PARTITION_SIZE = 512

class PagedAttention:
    """PagedAttention implementation for GCU, based on vllm-vllm_enrigin"""

    @staticmethod
    def get_supported_head_sizes() -> List[int]:
        return [32, 64, 80, 96, 112, 120, 128, 192, 256]

    @staticmethod
    def get_kv_cache_shape(
        num_blocks: int,
        block_size: int,
        num_kv_heads: int,
        head_size: int,
    ) -> Tuple[int, ...]:
        return (2, num_blocks, block_size * num_kv_heads * head_size)

    @staticmethod
    def split_kv_cache(
        kv_cache: torch.Tensor,
        num_kv_heads: int,
        head_size: int,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        x = 16 // kv_cache.element_size()
        num_blocks = kv_cache.shape[1]

        key_cache = kv_cache[0]
        key_cache = key_cache.view(num_blocks, num_kv_heads, head_size // x, -1, x)
        value_cache = kv_cache[1]
        value_cache = value_cache.view(num_blocks, num_kv_heads, head_size, -1)
        return key_cache, value_cache

    @staticmethod
    def write_to_paged_cache(
        key: torch.Tensor,
        value: torch.Tensor,
        key_cache: torch.Tensor,
        value_cache: torch.Tensor,
        slot_mapping: torch.Tensor,
        kv_cache_dtype: str = "auto",
        k_scale_float: float = 1.0,
        v_scale_float: float = 1.0,
        k_zero_float: float = 0.0,
        v_zero_float: float = 0.0,
    ) -> None:
        reshape_and_cache(
            key, value, key_cache, value_cache, slot_mapping,
            kv_cache_dtype, k_scale_float, v_scale_float, k_zero_float, v_zero_float
        )

    @staticmethod
    def forward_decode(
        query: torch.Tensor,
        key_cache: torch.Tensor,
        value_cache: torch.Tensor,
        block_tables: torch.Tensor,
        seq_lens: torch.Tensor,
        max_seq_len: int,
        kv_cache_dtype: str = "auto",
        num_kv_heads: int = None,
        scale: float = None,
        alibi_slopes: Optional[torch.Tensor] = None,
        k_scale_float: float = 1.0,
        v_scale_float: float = 1.0,
        tp_rank: int = 0,
        blocksparse_local_blocks: int = 0,
        blocksparse_vert_stride: int = 0,
        blocksparse_block_size: int = 64,
        blocksparse_head_sliding_step: int = 0,
        k_zero_float: float = 0.0,
        v_zero_float: float = 0.0,
        out_scales: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """Forward pass for decode phase using paged attention"""
        output = torch.empty_like(query)
        block_size = value_cache.shape[3] if len(value_cache.shape) > 3 else value_cache.shape[-1]
        num_seqs, num_heads, head_size = query.shape
        max_num_partitions = (max_seq_len + _PARTITION_SIZE - 1) // _PARTITION_SIZE
        use_v1 = max_seq_len <= 8192 and (
            max_num_partitions == 1 or num_seqs * num_heads > 512
        )

        # Validate block size
        if block_size not in {16, 32, 64, 128}:
            print(f"Warning: Invalid block size: {block_size}. Using fallback implementation.")
            return _fallback_decode_attention(query, key_cache, value_cache, block_tables, seq_lens, scale)

        if use_v1:
            # Run PagedAttention V1
            paged_attention_v1(
                output, query, key_cache, value_cache, num_kv_heads, scale,
                block_tables, seq_lens, block_size, max_seq_len, alibi_slopes,
                kv_cache_dtype, k_scale_float, v_scale_float, tp_rank,
                blocksparse_local_blocks, blocksparse_vert_stride, blocksparse_block_size,
                blocksparse_head_sliding_step, k_zero_float, v_zero_float, out_scales
            )
        else:
            # Run PagedAttention V2
            assert _PARTITION_SIZE % block_size == 0
            tmp_output = torch.empty(
                size=(num_seqs, num_heads, max_num_partitions, head_size),
                dtype=output.dtype,
                device=output.device,
            )
            exp_sums = torch.empty(
                size=(num_seqs, num_heads, max_num_partitions),
                dtype=torch.float32,
                device=output.device,
            )
            max_logits = torch.empty_like(exp_sums)
            paged_attention_v2(
                output, exp_sums, max_logits, tmp_output, query, key_cache, value_cache,
                num_kv_heads, scale, block_tables, seq_lens, block_size, max_seq_len,
                alibi_slopes, kv_cache_dtype, k_scale_float, v_scale_float, tp_rank,
                blocksparse_local_blocks, blocksparse_vert_stride, blocksparse_block_size,
                blocksparse_head_sliding_step, k_zero_float, v_zero_float, out_scales
            )

        return output

    @staticmethod
    def forward_prefix(
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        kv_cache_dtype: str,
        key_cache: torch.Tensor,
        value_cache: torch.Tensor,
        block_tables: torch.Tensor,
        query_start_loc: torch.Tensor,
        seq_lens_tensor: torch.Tensor,
        max_query_len: int,
        alibi_slopes: Optional[torch.Tensor] = None,
        sliding_window: Optional[int] = None,
        k_scale: Optional[torch.Tensor] = None,
        v_scale: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """Forward pass for prefix phase using context attention"""
        output = torch.empty_like(query)

        if is_gcu_available() and _GCU_OPS_AVAILABLE:
            try:
                torch.ops._C.context_attention_forward(
                    query, key, value, output, key_cache, value_cache, block_tables,
                    query_start_loc[:-1], seq_lens_tensor,
                    seq_lens_tensor - query_start_loc.diff(),
                    max_query_len, alibi_slopes, sliding_window,
                )
                return output
            except (AttributeError, RuntimeError) as e:
                print(f"GCU context_attention_forward failed, using fallback: {e}")

        # Fallback implementation
        return _fallback_context_attention(
            query, key, value, key_cache, value_cache, block_tables,
            query_start_loc, seq_lens_tensor, max_query_len, alibi_slopes
        )

def memory_efficient_attention(
    query: torch.Tensor,
    key: torch.Tensor,
    value: torch.Tensor,
    cu_seqlens_q: torch.Tensor,
    cu_seqlens_k: torch.Tensor,
    max_seqlen_q: int,
    max_seqlen_k: int,
    scale: float,
    causal: bool = True,
    alibi_slopes: Optional[torch.Tensor] = None,
    sliding_window: Optional[int] = None,
) -> torch.Tensor:
    """Memory efficient attention for prefill phase"""
    output = torch.empty_like(query)

    if is_gcu_available() and _GCU_OPS_AVAILABLE:
        try:
            # Try GCU memory efficient attention
            if alibi_slopes is not None:
                torch.ops._C.memory_efficient_attention_alibi(
                    output, query, key, value, alibi_slopes, 0.0, scale
                )
            else:
                # Use standard memory efficient attention
                torch.ops._C.memory_efficient_attention(
                    output, query, key, value, scale, causal
                )
            return output
        except (AttributeError, RuntimeError) as e:
            print(f"GCU memory_efficient_attention failed, using fallback: {e}")

    # Fallback to simple attention implementation
    return _simple_attention_varlen(
        query, key, value, cu_seqlens_q, cu_seqlens_k,
        max_seqlen_q, max_seqlen_k, scale, causal
    )

def paged_attention_v1(
    out: torch.Tensor,
    query: torch.Tensor,
    key_cache: torch.Tensor,
    value_cache: torch.Tensor,
    num_kv_heads: int,
    scale: float,
    block_tables: torch.Tensor,
    seq_lens: torch.Tensor,
    block_size: int,
    max_seq_len: int,
    alibi_slopes: Optional[torch.Tensor] = None,
    kv_cache_dtype: str = "auto",
    k_scale_float: float = 1.0,
    v_scale_float: float = 1.0,
    tp_rank: int = 0,
    blocksparse_local_blocks: int = 0,
    blocksparse_vert_stride: int = 0,
    blocksparse_block_size: int = 64,
    blocksparse_head_sliding_step: int = 0,
    k_zero_float: float = 0.0,
    v_zero_float: float = 0.0,
    out_scales: Optional[torch.Tensor] = None,
) -> None:
    """GCU implementation of paged attention v1 based on vllm-vllm_enrigin"""
    if is_gcu_available() and _GCU_OPS_AVAILABLE:
        try:
            torch.ops._C.paged_attention_v1(
                out, query, key_cache, value_cache, num_kv_heads, scale,
                block_tables, seq_lens, block_size, max_seq_len, alibi_slopes,
                kv_cache_dtype, k_scale_float, v_scale_float, tp_rank,
                blocksparse_local_blocks, blocksparse_vert_stride, blocksparse_block_size,
                blocksparse_head_sliding_step, k_zero_float, v_zero_float, out_scales
            )
            return
        except (AttributeError, RuntimeError) as e:
            print(f"GCU paged_attention_v1 failed, using fallback: {e}")

    # Fallback implementation
    _fallback_paged_attention_v1(
        out, query, key_cache, value_cache, num_kv_heads, scale,
        block_tables, seq_lens, block_size, max_seq_len, alibi_slopes
    )

def paged_attention_v2(
    out: torch.Tensor,
    exp_sum: torch.Tensor,
    max_logits: torch.Tensor,
    tmp_out: torch.Tensor,
    query: torch.Tensor,
    key_cache: torch.Tensor,
    value_cache: torch.Tensor,
    num_kv_heads: int,
    scale: float,
    block_tables: torch.Tensor,
    seq_lens: torch.Tensor,
    block_size: int,
    max_seq_len: int,
    alibi_slopes: Optional[torch.Tensor] = None,
    kv_cache_dtype: str = "auto",
    k_scale_float: float = 1.0,
    v_scale_float: float = 1.0,
    tp_rank: int = 0,
    blocksparse_local_blocks: int = 0,
    blocksparse_vert_stride: int = 0,
    blocksparse_block_size: int = 64,
    blocksparse_head_sliding_step: int = 0,
    k_zero_float: float = 0.0,
    v_zero_float: float = 0.0,
    out_scales: Optional[torch.Tensor] = None,
) -> None:
    """GCU implementation of paged attention v2 based on vllm-vllm_enrigin"""
    if is_gcu_available() and _GCU_OPS_AVAILABLE:
        try:
            torch.ops._C.paged_attention_v2(
                out, exp_sum, max_logits, tmp_out, query, key_cache, value_cache,
                num_kv_heads, scale, block_tables, seq_lens, block_size, max_seq_len,
                alibi_slopes, kv_cache_dtype, k_scale_float, v_scale_float, tp_rank,
                blocksparse_local_blocks, blocksparse_vert_stride, blocksparse_block_size,
                blocksparse_head_sliding_step, k_zero_float, v_zero_float, out_scales
            )
            return
        except (AttributeError, RuntimeError) as e:
            print(f"GCU paged_attention_v2 failed, using fallback: {e}")

    # Fallback implementation
    _fallback_paged_attention_v2(
        out, exp_sum, max_logits, tmp_out, query, key_cache, value_cache,
        num_kv_heads, scale, block_tables, seq_lens, block_size, max_seq_len, alibi_slopes
    )

def reshape_and_cache(
    key: torch.Tensor,
    value: torch.Tensor,
    key_cache: torch.Tensor,
    value_cache: torch.Tensor,
    slot_mapping: torch.Tensor,
    kv_cache_dtype: str = "auto",
    k_scale_float: float = 1.0,
    v_scale_float: float = 1.0,
    k_zero_float: float = 0.0,
    v_zero_float: float = 0.0,
) -> None:
    """GCU implementation of reshape and cache"""
    if is_gcu_available() and _GCU_OPS_AVAILABLE:
        try:
            torch.ops._C_cache_ops.reshape_and_cache(
                key, value, key_cache, value_cache, slot_mapping,
                kv_cache_dtype, k_scale_float, v_scale_float, k_zero_float, v_zero_float
            )
            return
        except (AttributeError, RuntimeError) as e:
            print(f"GCU reshape_and_cache failed, using fallback: {e}")

    # Fallback implementation
    _fallback_reshape_and_cache(key, value, key_cache, value_cache, slot_mapping)

def rotary_embedding(
    positions: torch.Tensor,
    query: torch.Tensor,
    key: torch.Tensor,
    head_size: int,
    cos_sin_cache: torch.Tensor,
    is_neox: bool,
) -> None:
    """GCU implementation of rotary embedding"""
    if query.numel() == 0:
        return

    if is_gcu_available() and _GCU_OPS_AVAILABLE:
        try:
            torch.ops._C.rotary_embedding(
                positions, query, key, head_size, cos_sin_cache, is_neox
            )
            return
        except (AttributeError, RuntimeError) as e:
            print(f"GCU rotary_embedding failed, using fallback: {e}")

    # Fallback implementation
    _fallback_rotary_embedding(positions, query, key, head_size, cos_sin_cache, is_neox)

def rms_norm(
    out: torch.Tensor,
    input: torch.Tensor,
    weight: torch.Tensor,
    epsilon: float
) -> None:
    """GCU implementation of RMS normalization"""
    if is_gcu_available() and _GCU_OPS_AVAILABLE:
        try:
            torch.ops._C.rms_norm(out, input, weight, epsilon)
            return
        except (AttributeError, RuntimeError) as e:
            print(f"GCU rms_norm failed, using fallback: {e}")

    # Fallback implementation
    variance = input.pow(2).mean(-1, keepdim=True)
    out.copy_(input * torch.rsqrt(variance + epsilon) * weight)

def fused_add_rms_norm(
    input: torch.Tensor,
    residual: torch.Tensor,
    weight: torch.Tensor,
    epsilon: float
) -> None:
    """GCU implementation of fused add + RMS normalization"""
    if is_gcu_available() and _GCU_OPS_AVAILABLE:
        try:
            torch.ops._C.fused_add_rms_norm(input, residual, weight, epsilon)
            return
        except (AttributeError, RuntimeError) as e:
            print(f"GCU fused_add_rms_norm failed, using fallback: {e}")

    # Fallback implementation
    input.add_(residual)
    variance = input.pow(2).mean(-1, keepdim=True)
    input.mul_(torch.rsqrt(variance + epsilon) * weight)

def silu_and_mul(out: torch.Tensor, input: torch.Tensor) -> None:
    """GCU implementation of SiLU activation with multiplication"""
    if is_gcu_available():
        # Use GCU optimized implementation if available
        # For now, use PyTorch implementation
        pass
    
    # Fallback to PyTorch implementation
    d = input.shape[-1] // 2
    out.copy_(torch.nn.functional.silu(input[..., :d]) * input[..., d:])

def copy_blocks(
    key_caches: List[torch.Tensor],
    value_caches: List[torch.Tensor],
    block_mapping: torch.Tensor,
) -> None:
    """GCU implementation of copy blocks"""
    if is_gcu_available():
        torch.ops._C_cache_ops.copy_blocks(key_caches, value_caches, block_mapping)
    else:
        raise NotImplementedError("GCU not available, copy blocks not supported")

def swap_blocks(
    src: torch.Tensor,
    dst: torch.Tensor,
    block_mapping: torch.Tensor
) -> None:
    """GCU implementation of swap blocks"""
    if is_gcu_available():
        torch.ops._C_cache_ops.swap_blocks(src, dst, block_mapping)
    else:
        raise NotImplementedError("GCU not available, swap blocks not supported")

# Flash attention functions for GCU
def flash_attn_varlen_func(
    q: torch.Tensor,
    k: torch.Tensor,
    v: torch.Tensor,
    cu_seqlens_q: torch.Tensor,
    cu_seqlens_k: torch.Tensor,
    max_seqlen_q: int,
    max_seqlen_k: int,
    dropout_p: float = 0.0,
    softmax_scale: Optional[float] = None,
    causal: bool = False,
    window_size: Optional[Tuple[int, int]] = None,
    alibi_slopes: Optional[torch.Tensor] = None,
    return_attn_probs: bool = False,
    block_table: Optional[torch.Tensor] = None,
) -> torch.Tensor:
    """GCU implementation of variable length flash attention"""
    try:
        if is_gcu_available():
            try:
                from flash_attn.vllm_flash_attn import flash_attn_varlen_func as gcu_flash_attn
                return gcu_flash_attn(
                    q, k, v, cu_seqlens_q, max_seqlen_q, cu_seqlens_k, max_seqlen_k,
                    dropout_p, softmax_scale, causal, window_size, alibi_slopes,
                    return_attn_probs, block_table
                )
            except ImportError:
                pass

        # Try standard flash attention
        from flash_attn import flash_attn_varlen_func as standard_flash_attn
        return standard_flash_attn(
            q, k, v, cu_seqlens_q, cu_seqlens_k, max_seqlen_q, max_seqlen_k,
            dropout_p, softmax_scale, causal, window_size, alibi_slopes, return_attn_probs
        )
    except ImportError:
        # Fallback to simple attention implementation
        return _simple_attention_varlen(
            q, k, v, cu_seqlens_q, cu_seqlens_k, max_seqlen_q, max_seqlen_k,
            softmax_scale, causal
        )

def _simple_attention_varlen(
    q: torch.Tensor,
    k: torch.Tensor,
    v: torch.Tensor,
    cu_seqlens_q: torch.Tensor,
    cu_seqlens_k: torch.Tensor,
    max_seqlen_q: int,
    max_seqlen_k: int,
    softmax_scale: Optional[float] = None,
    causal: bool = False,
) -> torch.Tensor:
    """Simple fallback attention implementation"""
    if softmax_scale is None:
        softmax_scale = 1.0 / (q.size(-1) ** 0.5)

    # Reshape tensors for batch processing
    batch_size = len(cu_seqlens_q) - 1
    outputs = []

    for i in range(batch_size):
        q_start, q_end = cu_seqlens_q[i], cu_seqlens_q[i + 1]
        k_start, k_end = cu_seqlens_k[i], cu_seqlens_k[i + 1]

        q_i = q[q_start:q_end]  # [seq_len_q, num_heads, head_dim]
        k_i = k[k_start:k_end]  # [seq_len_k, num_heads, head_dim]
        v_i = v[k_start:k_end]  # [seq_len_k, num_heads, head_dim]

        # Ensure dimensions are correct
        if q_i.dim() == 2:  # [seq_len_q, hidden_size]
            # Reshape to [seq_len_q, num_heads, head_dim]
            seq_len_q = q_i.size(0)
            hidden_size = q_i.size(1)
            num_heads = hidden_size // (hidden_size // 8)  # Assume 8 heads for now
            head_dim = hidden_size // num_heads
            q_i = q_i.view(seq_len_q, num_heads, head_dim)
            k_i = k_i.view(k_i.size(0), num_heads, head_dim)
            v_i = v_i.view(v_i.size(0), num_heads, head_dim)

        # Compute attention scores: [seq_len_q, num_heads, head_dim] @ [seq_len_k, num_heads, head_dim].T
        # Result should be [seq_len_q, num_heads, seq_len_k]
        scores = torch.matmul(q_i, k_i.transpose(-2, -1)) * softmax_scale

        # Apply causal mask if needed
        if causal and scores.size(-1) > 1:
            seq_len_q, seq_len_k = scores.size(0), scores.size(-1)
            # Create causal mask: [seq_len_q, seq_len_k]
            mask = torch.triu(torch.ones(seq_len_q, seq_len_k, device=q.device, dtype=torch.bool), diagonal=1)

            # Apply mask safely
            if is_gcu_available():
                # Use subtraction instead of masked_fill for GCU
                mask_value = torch.where(mask, -1e9, 0.0).to(scores.dtype)
                mask_value = mask_value.unsqueeze(1)  # [seq_len_q, 1, seq_len_k]
                scores = scores + mask_value
            else:
                mask = mask.unsqueeze(1)  # [seq_len_q, 1, seq_len_k]
                scores.masked_fill_(mask, float('-inf'))

        # Apply softmax
        attn_weights = torch.softmax(scores, dim=-1)

        # Apply attention to values: [seq_len_q, num_heads, seq_len_k] @ [seq_len_k, num_heads, head_dim]
        # Result should be [seq_len_q, num_heads, head_dim]
        output_i = torch.matmul(attn_weights, v_i)

        # Flatten back to [seq_len_q, hidden_size]
        output_i = output_i.view(output_i.size(0), -1)
        outputs.append(output_i)

    return torch.cat(outputs, dim=0)

def flash_attn_with_kvcache(
    q: torch.Tensor,
    k_cache: torch.Tensor,
    v_cache: torch.Tensor,
    k: Optional[torch.Tensor] = None,
    v: Optional[torch.Tensor] = None,
    rotary_cos: Optional[torch.Tensor] = None,
    rotary_sin: Optional[torch.Tensor] = None,
    cache_seqlens: Optional[torch.Tensor] = None,
    cache_batch_idx: Optional[torch.Tensor] = None,
    cache_leftpad: Optional[torch.Tensor] = None,
    block_table: Optional[torch.Tensor] = None,
    softmax_scale: Optional[float] = None,
    causal: bool = False,
    window_size: Optional[Tuple[int, int]] = None,
    rotary_interleaved: bool = True,
    alibi_slopes: Optional[torch.Tensor] = None,
) -> torch.Tensor:
    """GCU implementation of flash attention with KV cache"""
    try:
        if is_gcu_available():
            try:
                from flash_attn.vllm_flash_attn import flash_attn_with_kvcache as gcu_flash_attn_kv
                return gcu_flash_attn_kv(
                    q, k_cache, v_cache, k, v, rotary_cos, rotary_sin,
                    cache_seqlens, cache_batch_idx, cache_leftpad, block_table,
                    softmax_scale, causal, window_size, rotary_interleaved, alibi_slopes
                )
            except ImportError:
                pass

        # Try standard flash attention
        from flash_attn import flash_attn_with_kvcache as standard_flash_attn_kv
        return standard_flash_attn_kv(
            q, k_cache, v_cache, k, v, rotary_cos, rotary_sin,
            cache_seqlens, cache_batch_idx, cache_leftpad, block_table,
            softmax_scale, causal, window_size, rotary_interleaved, alibi_slopes
        )
    except ImportError:
        # Fallback to simple attention implementation
        return _simple_attention_with_kvcache(
            q, k_cache, v_cache, cache_seqlens, block_table, softmax_scale, causal
        )

def _simple_attention_with_kvcache(
    q: torch.Tensor,
    k_cache: torch.Tensor,
    v_cache: torch.Tensor,
    cache_seqlens: Optional[torch.Tensor] = None,
    block_table: Optional[torch.Tensor] = None,
    softmax_scale: Optional[float] = None,
    causal: bool = False,
) -> torch.Tensor:
    """Simple fallback attention implementation with KV cache"""
    if softmax_scale is None:
        softmax_scale = 1.0 / (q.size(-1) ** 0.5)

    # For simplicity, assume q is [batch_size, 1, num_heads, head_dim] for decode
    batch_size = q.size(0)
    seq_len = 1  # decode step

    outputs = []
    for i in range(batch_size):
        if cache_seqlens is not None:
            cache_len = cache_seqlens[i].item()
        else:
            cache_len = k_cache.size(1)  # Assume full cache

        q_i = q[i:i+1]  # [1, num_heads, head_dim]

        # Extract relevant cache for this sequence
        if block_table is not None:
            # For simplicity, just use the cache as-is
            k_i = k_cache[i, :cache_len]  # [cache_len, num_heads, head_dim]
            v_i = v_cache[i, :cache_len]  # [cache_len, num_heads, head_dim]
        else:
            k_i = k_cache[i, :cache_len]
            v_i = v_cache[i, :cache_len]

        # Compute attention scores
        scores = torch.matmul(q_i, k_i.transpose(-2, -1)) * softmax_scale

        # Apply causal mask (for decode, usually not needed as we only attend to past)
        if causal and scores.size(-1) > 1:
            if is_gcu_available():
                # Use a safer approach for GCU
                mask = torch.triu(torch.ones(seq_len, cache_len, device=q.device, dtype=scores.dtype), diagonal=1)
                scores = scores - mask * 1e9  # Large negative value instead of -inf
            else:
                mask = torch.triu(torch.ones(seq_len, cache_len, device=q.device), diagonal=1)
                scores.masked_fill_(mask.bool(), float('-inf'))

        # Apply softmax
        attn_weights = torch.softmax(scores, dim=-1)

        # Apply attention to values
        output_i = torch.matmul(attn_weights, v_i)
        outputs.append(output_i)

    return torch.cat(outputs, dim=0)

# Fallback implementations
def _fallback_paged_attention_v1(
    out: torch.Tensor,
    query: torch.Tensor,
    key_cache: torch.Tensor,
    value_cache: torch.Tensor,
    num_kv_heads: int,
    scale: float,
    block_tables: torch.Tensor,
    seq_lens: torch.Tensor,
    block_size: int,
    max_seq_len: int,
    alibi_slopes: Optional[torch.Tensor] = None,
) -> None:
    """Fallback implementation for paged attention v1"""
    batch_size = query.size(0)

    for i in range(batch_size):
        seq_len = seq_lens[i].item()
        if seq_len == 0:
            continue

        q_i = query[i:i+1]  # [1, num_heads, head_size]

        # Simple fallback: just use zeros for now
        # In a real implementation, you would extract from the paged cache
        out[i] = torch.zeros_like(q_i.squeeze(0))

def _fallback_paged_attention_v2(
    out: torch.Tensor,
    exp_sum: torch.Tensor,
    max_logits: torch.Tensor,
    tmp_out: torch.Tensor,
    query: torch.Tensor,
    key_cache: torch.Tensor,
    value_cache: torch.Tensor,
    num_kv_heads: int,
    scale: float,
    block_tables: torch.Tensor,
    seq_lens: torch.Tensor,
    block_size: int,
    max_seq_len: int,
    alibi_slopes: Optional[torch.Tensor] = None,
) -> None:
    """Fallback implementation for paged attention v2"""
    _fallback_paged_attention_v1(
        out, query, key_cache, value_cache, num_kv_heads, scale,
        block_tables, seq_lens, block_size, max_seq_len, alibi_slopes
    )

def _fallback_decode_attention(
    query: torch.Tensor,
    key_cache: torch.Tensor,
    value_cache: torch.Tensor,
    block_tables: torch.Tensor,
    seq_lens: torch.Tensor,
    scale: float,
) -> torch.Tensor:
    """Simple fallback for decode attention"""
    output = torch.empty_like(query)
    _fallback_paged_attention_v1(
        output, query, key_cache, value_cache, query.size(1), scale,
        block_tables, seq_lens, 64, seq_lens.max().item()
    )
    return output

def _fallback_context_attention(
    query: torch.Tensor,
    key: torch.Tensor,
    value: torch.Tensor,
    key_cache: torch.Tensor,
    value_cache: torch.Tensor,
    block_tables: torch.Tensor,
    query_start_loc: torch.Tensor,
    seq_lens_tensor: torch.Tensor,
    max_query_len: int,
    alibi_slopes: Optional[torch.Tensor] = None,
) -> torch.Tensor:
    """Fallback implementation for context attention"""
    # Simple fallback: standard attention without cache
    batch_size = len(query_start_loc) - 1
    outputs = []

    for i in range(batch_size):
        start_idx = query_start_loc[i].item()
        end_idx = query_start_loc[i + 1].item()

        q_i = query[start_idx:end_idx]
        k_i = key[start_idx:end_idx] if key is not None else q_i
        v_i = value[start_idx:end_idx] if value is not None else q_i

        # Standard attention
        scores = torch.matmul(q_i, k_i.transpose(-2, -1)) / (q_i.size(-1) ** 0.5)
        attn_weights = torch.softmax(scores, dim=-1)
        output_i = torch.matmul(attn_weights, v_i)
        outputs.append(output_i)

    return torch.cat(outputs, dim=0)

def _fallback_rotary_embedding(
    positions: torch.Tensor,
    query: torch.Tensor,
    key: torch.Tensor,
    head_size: int,
    cos_sin_cache: torch.Tensor,
    is_neox: bool,
) -> None:
    """Fallback implementation for rotary embedding"""
    # Simple fallback: apply rotary embedding using PyTorch operations
    num_tokens = positions.size(0)
    cos_sin = cos_sin_cache[positions]
    cos, sin = cos_sin.chunk(2, dim=-1)

    # Get original shapes
    query_shape = query.shape
    key_shape = key.shape

    # Ensure contiguous tensors
    query = query.contiguous()
    key = key.contiguous()

    # Calculate dimensions correctly
    if query.dim() == 2:  # [num_tokens, hidden_size]
        query_hidden_size = query.size(-1)
        key_hidden_size = key.size(-1)

        query_num_heads = query_hidden_size // head_size
        key_num_heads = key_hidden_size // head_size

        # Check if reshape is valid for both tensors
        if (query.numel() == num_tokens * query_num_heads * head_size and
            key.numel() == num_tokens * key_num_heads * head_size):
            query_reshaped = query.view(num_tokens, query_num_heads, head_size)
            key_reshaped = key.view(num_tokens, key_num_heads, head_size)
        else:
            # If reshape fails, just use original tensors
            query_reshaped = query
            key_reshaped = key
    else:
        query_reshaped = query
        key_reshaped = key

    # Apply rotation (simplified - just use cos/sin directly)
    # This is a simplified version, real rotary embedding is more complex
    if cos.dim() == 2:  # [num_tokens, head_size]
        cos = cos.unsqueeze(1)  # [num_tokens, 1, head_size]
        sin = sin.unsqueeze(1)  # [num_tokens, 1, head_size]

    try:
        query_rot = query_reshaped * cos + _rotate_half(query_reshaped) * sin
        key_rot = key_reshaped * cos + _rotate_half(key_reshaped) * sin

        # Copy back to original tensors
        query.copy_(query_rot.view(query_shape))
        key.copy_(key_rot.view(key_shape))
    except Exception as e:
        print(f"Rotary embedding fallback failed: {e}")
        # If all else fails, just leave tensors unchanged
        pass

def _rotate_half(x):
    """Rotate half the hidden dims of the input."""
    x1 = x[..., : x.shape[-1] // 2]
    x2 = x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)

def _fallback_reshape_and_cache(
    key: torch.Tensor,
    value: torch.Tensor,
    key_cache: torch.Tensor,
    value_cache: torch.Tensor,
    slot_mapping: torch.Tensor,
) -> None:
    """Fallback implementation for reshape and cache"""
    # Simple fallback: store key and value in cache using slot mapping
    num_tokens = key.size(0)

    for i in range(num_tokens):
        slot = slot_mapping[i].item()
        if slot >= 0:  # Valid slot
            try:
                # Flatten and store in cache
                if len(key_cache.shape) == 2:  # [num_slots, hidden_size]
                    key_cache[slot] = key[i].flatten()
                    value_cache[slot] = value[i].flatten()
                else:
                    # More complex cache structure, just copy what we can
                    key_cache[slot] = key[i]
                    value_cache[slot] = value[i]
            except Exception as e:
                print(f"Failed to cache at slot {slot}: {e}")
                # Skip this slot if there's an error
