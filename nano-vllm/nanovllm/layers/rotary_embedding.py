from functools import lru_cache
import torch
from torch import nn
from nanovllm.utils.gcu_utils import is_gcu_available
from nanovllm.utils.gcu_kernels import rotary_embedding


def apply_rotary_emb(
    x: torch.Tensor,
    cos: torch.Tensor,
    sin: torch.Tensor,
) -> torch.Tensor:
    cos = cos.unsqueeze(-2)
    sin = sin.unsqueeze(-2)
    x1, x2 = torch.chunk(x.to(torch.float32), 2, dim=-1)
    y1 = x1 * cos - x2 * sin
    y2 = x2 * cos + x1 * sin
    return torch.cat((y1, y2), dim=-1).to(x.dtype)


class RotaryEmbedding(nn.Module):

    def __init__(
        self,
        head_size: int,
        rotary_dim: int,
        max_position_embeddings: int,
        base: float,
    ) -> None:
        super().__init__()
        self.head_size = head_size
        assert rotary_dim == head_size
        inv_freq = 1.0 / (base**(torch.arange(0, rotary_dim, 2, dtype=torch.float) / rotary_dim))
        t = torch.arange(max_position_embeddings, dtype=torch.float)
        freqs = torch.einsum("i,j -> ij", t, inv_freq)
        cos = freqs.cos()
        sin = freqs.sin()
        cache = torch.cat((cos, sin), dim=-1)
        self.register_buffer("cos_sin_cache", cache, persistent=False)

    def forward(
        self,
        positions: torch.Tensor,
        query: torch.Tensor,
        key: torch.Tensor,
    ) -> tuple[torch.Tensor, torch.Tensor]:
        """Forward pass with GCU/CUDA compatibility"""
        if is_gcu_available():
            # Use GCU optimized rotary embedding
            query_clone = query.clone()
            key_clone = key.clone()
            rotary_embedding(positions, query_clone, key_clone, self.head_size, self.cos_sin_cache, False)
            return query_clone, key_clone
        else:
            # Use PyTorch implementation
            num_tokens = positions.size(0)
            cos_sin = self.cos_sin_cache[positions]
            cos, sin = cos_sin.chunk(2, dim=-1)
            query_shape = query.shape
            query = query.view(num_tokens, -1, self.head_size)
            query = apply_rotary_emb(query, cos, sin).view(query_shape)
            key_shape = key.shape
            key = key.view(num_tokens, -1, self.head_size)
            key = apply_rotary_emb(key, cos, sin).view(key_shape)
            return query, key


@lru_cache(1)
def get_rope(
    head_size: int,
    rotary_dim: int,
    max_position: int,
    base: float,
    rope_scaling: dict | None = None,
):
    assert rope_scaling is None
    rotary_emb = RotaryEmbedding(head_size, rotary_dim, max_position, base)
    return rotary_emb
