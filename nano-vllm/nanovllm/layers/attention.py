import torch
from torch import nn

from nanovllm.utils.context import get_context
from nanovllm.utils.gcu_utils import is_gcu_available
from nanovllm.utils.gcu_kernels import (
    PagedAttention, memory_efficient_attention, reshape_and_cache
)

# Import triton only if not using GCU
if not is_gcu_available():
    try:
        import triton
        import triton.language as tl
        _TRITON_AVAILABLE = True
    except ImportError:
        _TRITON_AVAILABLE = False
else:
    _TRITON_AVAILABLE = False


# Triton kernel for CUDA (only if triton is available and not using GCU)
if _TRITON_AVAILABLE and not is_gcu_available():
    @triton.jit
    def store_kvcache_kernel(
        key_ptr,
        key_stride,
        value_ptr,
        value_stride,
        k_cache_ptr,
        v_cache_ptr,
        slot_mapping_ptr,
        D: tl.constexpr,
    ):
        idx = tl.program_id(0)
        key_offsets = idx * key_stride + tl.arange(0, D)
        value_offsets = idx * value_stride + tl.arange(0, D)
        key = tl.load(key_ptr + key_offsets)
        value = tl.load(value_ptr + value_offsets)
        slot = tl.load(slot_mapping_ptr + idx)
        cache_offsets = slot * D + tl.arange(0, D)
        tl.store(k_cache_ptr + cache_offsets, key)
        tl.store(v_cache_ptr + cache_offsets, value)

def store_kvcache(key: torch.Tensor, value: torch.Tensor, k_cache: torch.Tensor, v_cache: torch.Tensor, slot_mapping: torch.Tensor):
    """Store key-value cache using appropriate backend"""
    if is_gcu_available():
        # Use GCU reshape_and_cache operation
        reshape_and_cache(key, value, k_cache, v_cache, slot_mapping)
    elif _TRITON_AVAILABLE:
        # Use triton kernel for CUDA
        N, num_heads, head_dim = key.shape
        D = num_heads * head_dim
        assert key.stride(-1) == 1 and value.stride(-1) == 1
        assert key.stride(1) == head_dim and value.stride(1) == head_dim
        assert k_cache.stride(1) == D and v_cache.stride(1) == D
        assert slot_mapping.numel() == N
        store_kvcache_kernel[(N,)](key, key.stride(0), value, value.stride(0), k_cache, v_cache, slot_mapping, D)
    else:
        # Fallback implementation
        N, num_heads, head_dim = key.shape
        for i in range(N):
            slot = slot_mapping[i].item()
            k_cache[slot] = key[i].flatten()
            v_cache[slot] = value[i].flatten()


class Attention(nn.Module):

    def __init__(
        self,
        num_heads,
        head_dim,
        scale,
        num_kv_heads,
    ):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = head_dim
        self.scale = scale
        self.num_kv_heads = num_kv_heads
        self.k_cache = self.v_cache = torch.tensor([])

    def forward(self, q: torch.Tensor, k: torch.Tensor, v: torch.Tensor):
        """Forward pass with GCU/CUDA compatibility using PagedAttention"""
        o: torch.Tensor
        q = q.view(-1, self.num_heads, self.head_dim)
        k = k.view(-1, self.num_kv_heads, self.head_dim)
        v = v.view(-1, self.num_kv_heads, self.head_dim)
        context = get_context()
        k_cache, v_cache = self.k_cache, self.v_cache

        # Store KV cache if available
        if k_cache.numel() and v_cache.numel():
            # Split the combined cache into separate key and value caches
            if len(k_cache.shape) == 2:  # Combined cache format [2, num_blocks, ...]
                key_cache, value_cache = PagedAttention.split_kv_cache(
                    k_cache, self.num_kv_heads, self.head_dim
                )
            else:
                key_cache, value_cache = k_cache, v_cache

            # Write to paged cache
            PagedAttention.write_to_paged_cache(
                k, v, key_cache, value_cache, context.slot_mapping
            )
        else:
            key_cache, value_cache = k_cache, v_cache

        if context.is_prefill:
            if context.block_tables is not None:    # prefix cache
                # Use prefix attention for cached prefill
                o = PagedAttention.forward_prefix(
                    q, k, v, "auto", key_cache, value_cache,
                    context.block_tables, context.cu_seqlens_q,
                    context.cu_seqlens_k, context.max_seqlen_q
                )
            else:
                # Use memory efficient attention for regular prefill
                o = memory_efficient_attention(
                    q, k, v,
                    context.cu_seqlens_q, context.cu_seqlens_k,
                    context.max_seqlen_q, context.max_seqlen_k,
                    self.scale, causal=True
                )
        else:    # decode
            # Use paged attention for decode
            o = PagedAttention.forward_decode(
                q, key_cache, value_cache,
                context.block_tables, context.context_lens,
                context.context_lens.max().item() if context.context_lens.numel() > 0 else 1,
                num_kv_heads=self.num_kv_heads,
                scale=self.scale
            )

        o = o.view(-1, self.num_heads * self.head_dim)
        return o
