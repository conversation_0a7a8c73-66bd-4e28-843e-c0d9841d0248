import torch
from torch import nn
from nanovllm.utils.gcu_utils import is_gcu_available
from nanovllm.utils.gcu_kernels import rms_norm, fused_add_rms_norm


class RMSNorm(nn.Module):

    def __init__(
        self,
        hidden_size: int,
        eps: float = 1e-6,
    ) -> None:
        super().__init__()
        self.hidden_size = hidden_size
        self.eps = eps
        self.weight = nn.Parameter(torch.ones(hidden_size))

    def rms_forward(
        self,
        x: torch.Tensor,
    ) -> torch.Tensor:
        """RMS normalization with GCU/CUDA compatibility"""
        if is_gcu_available():
            # Use GCU optimized RMS norm
            out = torch.empty_like(x)
            rms_norm(out, x, self.weight, self.eps)
            return out
        else:
            # Use PyTorch implementation
            orig_dtype = x.dtype
            x = x.to(torch.float32)
            var = x.pow(2).mean(dim=-1, keepdim=True)
            x.mul_(torch.rsqrt(var + self.eps))
            x = x.to(orig_dtype).mul_(self.weight)
            return x

    def add_rms_forward(
        self,
        x: torch.Tensor,
        residual: torch.Tensor,
    ) -> tuple[torch.Tensor, torch.Tensor]:
        """Fused add + RMS normalization with GCU/CUDA compatibility"""
        if is_gcu_available():
            # Use GCU optimized fused add + RMS norm
            fused_add_rms_norm(x, residual, self.weight, self.eps)
            return x, residual
        else:
            # Use PyTorch implementation
            orig_dtype = x.dtype
            x = x.to(torch.float32).add_(residual.to(torch.float32))
            residual = x.to(orig_dtype)
            var = x.pow(2).mean(dim=-1, keepdim=True)
            x.mul_(torch.rsqrt(var + self.eps))
            x = x.to(orig_dtype).mul_(self.weight)
            return x, residual

    def forward(
        self,
        x: torch.Tensor,
        residual: torch.Tensor | None = None,
    ) -> torch.Tensor | tuple[torch.Tensor, torch.Tensor]:
        if residual is None:
            return self.rms_forward(x)
        else:
            return self.add_rms_forward(x, residual)
