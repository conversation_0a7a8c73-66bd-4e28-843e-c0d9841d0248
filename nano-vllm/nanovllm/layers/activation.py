import torch
from torch import nn
import torch.nn.functional as F
from nanovllm.utils.gcu_utils import is_gcu_available
from nanovllm.utils.gcu_kernels import silu_and_mul


class SiluAndMul(nn.Module):

    def __init__(self):
        super().__init__()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """SiLU activation with multiplication, GCU/CUDA compatible"""
        if is_gcu_available():
            # Use GCU optimized SiLU and mul
            out = torch.empty_like(x[..., :x.shape[-1]//2])
            silu_and_mul(out, x)
            return out
        else:
            # Use PyTorch implementation
            x, y = x.chunk(2, -1)
            return F.silu(x) * y
